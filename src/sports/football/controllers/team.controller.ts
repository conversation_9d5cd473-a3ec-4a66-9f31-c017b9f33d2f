import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth } from '@nestjs/swagger';
import { TeamService } from '../services/team.service';
import { GetTeamsDto, PaginatedTeamsResponse, TeamResponseDto } from '../models/team.dto';
import { TeamStatisticsService } from '../services/team-statistics.service';
import { GetTeamStatisticsDto, TeamStatisticsResponseDto } from '../models/team-statistics.dto';
import { JwtAuthGuard } from '../../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../auth/guards/roles.guard';
import { TierAccessGuard, ApiUsageGuard } from '../../../auth/guards/tier-access.guard';
import { Public } from '../../../auth/decorators/auth.decorators';

@ApiTags('Football - Teams')
@Controller('football/teams')
@UseGuards(JwtAuthGuard, RolesGuard, TierAccessGuard, ApiUsageGuard)
export class TeamController {
    constructor(
        private readonly teamService: TeamService,
        private readonly teamStatisticsService: TeamStatisticsService
    ) { }


    /**
     * Get teams with pagination and filters
     * @param query - Query parameters (league, season, country, page, limit)
     * @returns Paginated list of teams
     */
    @ApiBearerAuth()
    @Get()
    async getTeams(@Query() query: GetTeamsDto): Promise<PaginatedTeamsResponse> {
        return this.teamService.getTeams(query);
    }

    /**
     * Get team statistics by league, season, and team
     * @param query - Query parameters (league, season, team)
     * @returns Team statistics
     */
    @ApiBearerAuth()
    @Get('statistics')
    async getTeamStatistics(
        @Query() query: GetTeamStatisticsDto,
    ): Promise<{ data: TeamStatisticsResponseDto; status: number }> {
        return this.teamStatisticsService.getTeamStatistics(query);
    }

    @ApiBearerAuth()
    @Get(':externalId')
    async getTeamById(@Param('externalId') externalId: number): Promise<{ data: TeamResponseDto; status: number }> {
        const team = await this.teamService.getTeamById(externalId);
        return { data: team, status: 200 };
    }
}