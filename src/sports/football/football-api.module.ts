import { Modu<PERSON> } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { FootballModule } from './football.module';
import { FixtureController } from './controllers/fixture.controller';
import { LeagueController } from './controllers/league.controller';
import { TeamController } from './controllers/team.controller';
import { AuthModule } from '../../auth/auth.module';
import { ApiUsageInterceptor, UserActivityInterceptor, RequestLoggingInterceptor, RateLimitInfoInterceptor } from '../../auth/interceptors/api-usage.interceptor';

// Football API Module - Contains controllers for HTTP endpoints
@Module({
  imports: [FootballModule, AuthModule],
  controllers: [
    FixtureController,
    LeagueController,
    TeamController,
  ],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: ApiUsageInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: UserActivityInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: RequestLoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: RateLimitInfoInterceptor,
    },
  ],
})
export class FootballApiModule { }
