import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';

// Entities
import { SystemUser, RegisteredUser, RefreshToken } from './entities';

// Services
import { AuthService } from './services/auth.service';
import { UserService } from './services/user.service';
import { RegisteredUserService } from './services/registered-user.service';
import { AdminSeederService } from './services/admin-seeder.service';
import { AuditLogService } from './services/audit-log.service';

// Controllers
import { RegisteredUserController } from './controllers/registered-user.controller';

// Strategies
import { JwtStrategy } from './strategies/jwt.strategy';

// Guards
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { TierAccessGuard, ApiUsageGuard, PremiumFeatureGuard, EnterpriseFeatureGuard, EmailVerificationGuard } from './guards/tier-access.guard';

// Interceptors
import { ApiUsageInterceptor, UserActivityInterceptor, RequestLoggingInterceptor, RateLimitInfoInterceptor } from './interceptors/api-usage.interceptor';

/**
 * Auth Module
 * Shared authentication logic and services
 */
@Module({
    imports: [
        // TypeORM entities
        TypeOrmModule.forFeature([
            SystemUser,
            RegisteredUser,
            RefreshToken,
        ]),

        // Passport configuration
        PassportModule.register({ defaultStrategy: 'jwt' }),

        // JWT configuration
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                secret: configService.get<string>('JWT_SECRET', 'default-jwt-secret'),
                signOptions: {
                    expiresIn: configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
                },
            }),
            inject: [ConfigService],
        }),

        // Throttler configuration
        ThrottlerModule.forRoot([
            {
                ttl: 60000, // 1 minute
                limit: 100, // 100 requests per minute (default)
            },
        ]),
    ],

    controllers: [
        RegisteredUserController,
    ],

    providers: [
        // Services
        AuthService,
        UserService,
        RegisteredUserService,
        AdminSeederService,
        AuditLogService,

        // Strategies
        JwtStrategy,

        // Guards
        JwtAuthGuard,
        RolesGuard,
        TierAccessGuard,
        ApiUsageGuard,
        PremiumFeatureGuard,
        EnterpriseFeatureGuard,
        EmailVerificationGuard,

        // Interceptors
        ApiUsageInterceptor,
        UserActivityInterceptor,
        RequestLoggingInterceptor,
        RateLimitInfoInterceptor,
    ],

    exports: [
        // Services
        AuthService,
        UserService,
        RegisteredUserService,

        // Guards
        JwtAuthGuard,
        RolesGuard,
        TierAccessGuard,
        ApiUsageGuard,
        PremiumFeatureGuard,
        EnterpriseFeatureGuard,
        EmailVerificationGuard,

        // Interceptors
        ApiUsageInterceptor,
        UserActivityInterceptor,
        RequestLoggingInterceptor,
        RateLimitInfoInterceptor,

        // TypeORM repositories (for other modules)
        TypeOrmModule,
    ],
})
export class AuthModule { }
