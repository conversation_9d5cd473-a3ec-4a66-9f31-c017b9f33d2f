import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';

// Sub-modules
import { SystemModule } from './system/system.module';
import { UsersModule } from './users/users.module';

// Shared services
import { AdminSeederService } from './shared/services/admin-seeder.service';

/**
 * Main Authentication Module
 * Orchestrates all authentication functionality through sub-modules
 */
@Module({
    imports: [
        // Configuration
        ConfigModule,

        // Rate limiting
        ThrottlerModule.forRoot([
            {
                name: 'short',
                ttl: 1000,
                limit: 3,
            },
            {
                name: 'medium',
                ttl: 10000,
                limit: 20
            },
            {
                name: 'long',
                ttl: 60000,
                limit: 100
            }
        ]),

        // Sub-modules
        SystemModule,
        UsersModule,
    ],

    providers: [
        // Global services
        AdminSeederService,
    ],

    exports: [
        // Export sub-modules
        SystemModule,
        UsersModule,
        
        // Export global services
        AdminSeederService,
    ],
})
export class AuthModule {}
