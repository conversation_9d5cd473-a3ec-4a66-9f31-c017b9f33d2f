import {
    Controller,
    Post,
    Body,
    HttpCode,
    HttpStatus,
    UseGuards,
    Req,
    Get
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { SystemAuthService } from '../services/system-auth.service';
import {
    SystemUserLoginDto,
    SystemUserCreateDto,
    RefreshTokenDto,
    TokenPairDto,
    SystemAuthResponseDto,
    SystemUserProfileDto,
    DeviceInfoDto
} from '../dto/system-auth.dto';
import { SystemJwtAuthGuard } from '../guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../guards/system-roles.guard';
import { AdminOnly, GetCurrentUser } from '../../core/decorators/auth.decorators';
import { SystemUser } from '../entities/system-user.entity';
import { SystemRole } from '../../core/types/auth.types';

/**
 * System Authentication Controller
 * Handles authentication for system users (admin, editor)
 */
@ApiTags('System Authentication')
@Controller('system-auth')
export class SystemAuthController {
    constructor(private readonly systemAuthService: SystemAuthService) { }

    @ApiOperation({
        summary: 'System User Login',
        description: `
        Login endpoint for system users (admin, editor).

        **Features:**
        - Username/password authentication
        - JWT token generation
        - Device tracking
        - Audit logging
        - Refresh token support

        **Security:**
        - Rate limiting applied
        - Password validation
        - Account status verification
        - Login attempt logging
        `
    })
    @ApiBody({ type: SystemUserLoginDto })
    @ApiResponse({
        status: 200,
        description: 'Login successful',
        type: SystemAuthResponseDto
    })
    @ApiResponse({
        status: 401,
        description: 'Invalid credentials'
    })
    @ApiResponse({
        status: 429,
        description: 'Too many login attempts'
    })
    @Post('login')
    @HttpCode(HttpStatus.OK)
    async login(
        @Body() loginDto: SystemUserLoginDto,
        @Req() req: Request
    ): Promise<TokenPairDto> {
        const deviceInfo: DeviceInfoDto = {
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            deviceInfo: `${req.get('User-Agent')?.split(' ')[0]} on ${req.get('User-Agent')?.includes('Windows') ? 'Windows' : 'Unknown'}`
        };

        return this.systemAuthService.login(loginDto, deviceInfo);
    }

    @ApiOperation({
        summary: 'Create System User',
        description: `
        Create a new system user (admin only).

        **Features:**
        - Admin-only access
        - Role assignment
        - Password hashing
        - Email validation
        - Username uniqueness check

        **Roles:**
        - admin: Full system access
        - editor: Content management access
        `
    })
    @ApiBody({ type: SystemUserCreateDto })
    @ApiResponse({
        status: 201,
        description: 'System user created successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 409,
        description: 'Username or email already exists'
    })
    @ApiResponse({
        status: 403,
        description: 'Admin access required'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard, SystemRolesGuard)
    @AdminOnly()
    @Post('create-user')
    async createUser(@Body() createUserDto: SystemUserCreateDto): Promise<{
        message: string;
        user: SystemUserProfileDto
    }> {
        const user = await this.systemAuthService.createUser(createUserDto);

        return {
            message: 'System user created successfully',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                fullName: user.fullName,
                role: user.role as SystemRole,
                isActive: user.isActive,
                lastLoginAt: user.lastLoginAt,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }
        };
    }

    @ApiOperation({
        summary: 'Refresh Access Token',
        description: `
        Refresh an expired access token using a valid refresh token.

        **Features:**
        - Refresh token validation
        - New access token generation
        - Token expiry checking
        - User status verification
        `
    })
    @ApiBody({ type: RefreshTokenDto })
    @ApiResponse({
        status: 200,
        description: 'Access token refreshed successfully',
        schema: {
            type: 'object',
            properties: {
                accessToken: {
                    type: 'string',
                    description: 'New JWT access token'
                }
            }
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Invalid or expired refresh token'
    })
    @Post('refresh')
    @HttpCode(HttpStatus.OK)
    async refreshToken(@Body() refreshTokenDto: RefreshTokenDto): Promise<{ accessToken: string }> {
        return this.systemAuthService.refreshAccessToken(refreshTokenDto.refreshToken);
    }

    @ApiOperation({
        summary: 'Logout',
        description: `
        Logout current session by revoking the refresh token.

        **Features:**
        - Refresh token revocation
        - Session termination
        - Audit logging
        `
    })
    @ApiBody({ type: RefreshTokenDto })
    @ApiResponse({
        status: 200,
        description: 'Logout successful',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Logout successful'
                }
            }
        }
    })
    @Post('logout')
    @HttpCode(HttpStatus.OK)
    async logout(@Body() refreshTokenDto: RefreshTokenDto): Promise<{ message: string }> {
        await this.systemAuthService.logout(refreshTokenDto.refreshToken);
        return { message: 'Logout successful' };
    }

    @ApiOperation({
        summary: 'Logout from All Devices',
        description: `
        Logout from all devices by revoking all refresh tokens for the current user.

        **Features:**
        - All refresh tokens revocation
        - Multi-device session termination
        - Security enhancement
        `
    })
    @ApiResponse({
        status: 200,
        description: 'Logged out from all devices successfully',
        schema: {
            type: 'object',
            properties: {
                message: {
                    type: 'string',
                    example: 'Logged out from all devices successfully'
                }
            }
        }
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard)
    @Post('logout-all')
    @HttpCode(HttpStatus.OK)
    async logoutFromAllDevices(@GetCurrentUser() user: SystemUser): Promise<{ message: string }> {
        await this.systemAuthService.logoutFromAllDevices(user.id);
        return { message: 'Logged out from all devices successfully' };
    }

    @ApiOperation({
        summary: 'Get Current User Profile',
        description: `
        Get the profile information of the currently authenticated system user.

        **Features:**
        - Current user information
        - Role and permissions
        - Account status
        - Last login information
        `
    })
    @ApiResponse({
        status: 200,
        description: 'User profile retrieved successfully',
        type: SystemUserProfileDto
    })
    @ApiResponse({
        status: 401,
        description: 'Authentication required'
    })
    @ApiBearerAuth()
    @UseGuards(SystemJwtAuthGuard)
    @Get('profile')
    async getProfile(@GetCurrentUser() user: SystemUser): Promise<SystemUserProfileDto> {
        return {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role as SystemRole,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }
}
