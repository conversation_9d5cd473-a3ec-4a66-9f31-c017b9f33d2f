import { Is<PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RegisteredUserTier } from '../types/auth.types';

/**
 * Registered User Registration DTO
 */
export class RegisteredUserRegisterDto {
    @ApiProperty({
        description: 'Username for the account',
        example: 'john_doe',
        minLength: 3,
        maxLength: 50
    })
    @IsString()
    @MinLength(3)
    @MaxLength(50)
    username: string;

    @ApiProperty({
        description: 'Email address',
        example: '<EMAIL>'
    })
    @IsEmail()
    email: string;

    @ApiProperty({
        description: 'Password for the account',
        example: 'SecurePassword123!',
        minLength: 8,
        maxLength: 128
    })
    @IsString()
    @MinLength(8)
    @MaxLength(128)
    password: string;

    @ApiPropertyOptional({
        description: 'Full name of the user',
        example: '<PERSON>',
        maxLength: 100
    })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    fullName?: string;

    @ApiPropertyOptional({
        description: 'First name',
        example: 'John',
        maxLength: 50
    })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    firstName?: string;

    @ApiPropertyOptional({
        description: 'Last name',
        example: 'Doe',
        maxLength: 50
    })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    lastName?: string;

    @ApiPropertyOptional({
        description: 'Display name',
        example: 'JohnD',
        maxLength: 50
    })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    displayName?: string;
}

/**
 * Registered User Login DTO
 */
export class RegisteredUserLoginDto {
    @ApiProperty({
        description: 'Username or email',
        example: 'john_doe'
    })
    @IsString()
    @MinLength(3)
    @MaxLength(100)
    usernameOrEmail: string;

    @ApiProperty({
        description: 'Password',
        example: 'SecurePassword123!'
    })
    @IsString()
    @MinLength(6)
    password: string;
}

/**
 * Email Verification DTO
 */
export class EmailVerificationDto {
    @ApiProperty({
        description: 'Email verification token',
        example: 'abc123def456'
    })
    @IsString()
    token: string;
}

/**
 * Resend Email Verification DTO
 */
export class ResendEmailVerificationDto {
    @ApiProperty({
        description: 'Email address to resend verification',
        example: '<EMAIL>'
    })
    @IsEmail()
    email: string;
}

/**
 * Password Reset Request DTO
 */
export class PasswordResetRequestDto {
    @ApiProperty({
        description: 'Email address for password reset',
        example: '<EMAIL>'
    })
    @IsEmail()
    email: string;
}

/**
 * Password Reset DTO
 */
export class PasswordResetDto {
    @ApiProperty({
        description: 'Password reset token',
        example: 'reset123token456'
    })
    @IsString()
    token: string;

    @ApiProperty({
        description: 'New password',
        example: 'NewSecurePassword123!',
        minLength: 8,
        maxLength: 128
    })
    @IsString()
    @MinLength(8)
    @MaxLength(128)
    newPassword: string;
}

/**
 * Update Profile DTO
 */
export class UpdateProfileDto {
    @ApiPropertyOptional({
        description: 'Full name',
        example: 'John Doe Updated'
    })
    @IsOptional()
    @IsString()
    @MaxLength(100)
    fullName?: string;

    @ApiPropertyOptional({
        description: 'First name',
        example: 'John'
    })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    firstName?: string;

    @ApiPropertyOptional({
        description: 'Last name',
        example: 'Doe'
    })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    lastName?: string;

    @ApiPropertyOptional({
        description: 'Display name',
        example: 'JohnD_Updated'
    })
    @IsOptional()
    @IsString()
    @MaxLength(50)
    displayName?: string;
}

/**
 * Change Password DTO
 */
export class ChangePasswordDto {
    @ApiProperty({
        description: 'Current password',
        example: 'CurrentPassword123!'
    })
    @IsString()
    @MinLength(6)
    currentPassword: string;

    @ApiProperty({
        description: 'New password',
        example: 'NewPassword123!',
        minLength: 8,
        maxLength: 128
    })
    @IsString()
    @MinLength(8)
    @MaxLength(128)
    newPassword: string;
}

/**
 * Registered User Profile Response DTO
 */
export class RegisteredUserProfileDto {
    @ApiProperty({ description: 'User ID', example: 1 })
    id: number;

    @ApiProperty({ description: 'Username', example: 'john_doe' })
    username: string;

    @ApiProperty({ description: 'Email address', example: '<EMAIL>' })
    email: string;

    @ApiProperty({ description: 'Full name', example: 'John Doe' })
    fullName: string;

    @ApiProperty({ description: 'First name', example: 'John', required: false })
    firstName?: string;

    @ApiProperty({ description: 'Last name', example: 'Doe', required: false })
    lastName?: string;

    @ApiProperty({ description: 'Display name', example: 'JohnD', required: false })
    displayName?: string;

    @ApiProperty({ 
        description: 'User tier', 
        example: 'free',
        enum: RegisteredUserTier
    })
    tier: RegisteredUserTier;

    @ApiProperty({ description: 'Account active status', example: true })
    isActive: boolean;

    @ApiProperty({ description: 'Email verification status', example: true })
    isEmailVerified: boolean;

    @ApiProperty({ description: 'API calls used this month', example: 150 })
    apiCallsUsed: number;

    @ApiProperty({ 
        description: 'API calls limit per month', 
        example: 1000,
        nullable: true
    })
    apiCallsLimit: number | null;

    @ApiProperty({ 
        description: 'API calls remaining this month', 
        example: 850,
        nullable: true
    })
    apiCallsRemaining: number | null;

    @ApiProperty({ description: 'Subscription active status', example: true })
    hasActiveSubscription: boolean;

    @ApiProperty({ 
        description: 'Subscription end date', 
        example: '2024-12-31T23:59:59.000Z',
        required: false
    })
    subscriptionEndDate?: Date;

    @ApiProperty({ description: 'Last login timestamp', required: false })
    lastLoginAt?: Date;

    @ApiProperty({ description: 'Account creation timestamp' })
    createdAt: Date;
}

/**
 * Registered User Auth Response DTO
 */
export class RegisteredUserAuthResponseDto {
    @ApiProperty({ description: 'Access token' })
    accessToken: string;

    @ApiProperty({ description: 'Refresh token' })
    refreshToken: string;

    @ApiProperty({ description: 'User profile information' })
    user: RegisteredUserProfileDto;
}
