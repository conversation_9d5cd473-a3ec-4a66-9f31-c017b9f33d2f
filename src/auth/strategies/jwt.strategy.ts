import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemUser } from '../entities/system-user.entity';
import { RegisteredUser } from '../entities/registered-user.entity';
import { SystemUserJwtPayload, RegisteredUserJwtPayload, UserType, JwtPayload } from '../types/auth.types';

/**
 * JWT Strategy for validating access tokens
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor(
        private configService: ConfigService,
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        @InjectRepository(RegisteredUser)
        private readonly registeredUserRepository: Repository<RegisteredUser>,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get<string>('JWT_SECRET', 'default-jwt-secret'),
        });
    }

    /**
     * Validate JWT payload and return user context
     */
    async validate(payload: JwtPayload): Promise<SystemUser | RegisteredUser> {
        // Validate payload structure
        if (!payload.sub || !payload.userType) {
            throw new UnauthorizedException('Invalid token payload');
        }

        // Handle different user types
        if (payload.userType === UserType.SYSTEM) {
            return this.validateSystemUser(payload as SystemUserJwtPayload);
        } else if (payload.userType === UserType.REGISTERED) {
            return this.validateRegisteredUser(payload as RegisteredUserJwtPayload);
        } else {
            throw new UnauthorizedException('Unsupported user type');
        }
    }

    /**
     * Validate system user JWT payload
     */
    private async validateSystemUser(payload: SystemUserJwtPayload): Promise<SystemUser> {
        // Find user in database
        const user = await this.systemUserRepository.findOne({
            where: {
                id: payload.sub,
                isActive: true
            }
        });

        if (!user) {
            throw new UnauthorizedException('System user not found or inactive');
        }

        // Verify role matches token
        if (user.role !== payload.role) {
            throw new UnauthorizedException('System user role mismatch');
        }

        // Update last login time
        user.lastLoginAt = new Date();
        await this.systemUserRepository.save(user);

        return user;
    }

    /**
     * Validate registered user JWT payload
     */
    private async validateRegisteredUser(payload: RegisteredUserJwtPayload): Promise<RegisteredUser> {
        // Find user in database
        const user = await this.registeredUserRepository.findOne({
            where: {
                id: payload.sub,
                isActive: true
            }
        });

        if (!user) {
            throw new UnauthorizedException('Registered user not found or inactive');
        }

        // Verify tier matches token
        if (user.tier !== payload.tier) {
            throw new UnauthorizedException('User tier mismatch');
        }

        // Verify email verification status matches token
        if (user.isEmailVerified !== payload.isEmailVerified) {
            throw new UnauthorizedException('Email verification status mismatch');
        }

        // Update last login timestamp
        user.lastLoginAt = new Date();
        await this.registeredUserRepository.save(user);

        return user;
    }
}
