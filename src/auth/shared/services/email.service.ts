import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as fs from 'fs';
import * as path from 'path';
import * as handlebars from 'handlebars';

/**
 * Email Service
 * Handles sending emails for authentication and user management
 */
@Injectable()
export class EmailService {
    private readonly logger = new Logger(EmailService.name);
    private transporter: nodemailer.Transporter;

    constructor(private configService: ConfigService) {
        this.initializeTransporter();
    }

    private async initializeTransporter() {
        try {
            this.transporter = nodemailer.createTransport({
                host: this.configService.get<string>('SMTP_HOST', 'localhost'),
                port: this.configService.get<number>('SMTP_PORT', 587),
                secure: false,
                auth: {
                    user: this.configService.get<string>('SMTP_USER'),
                    pass: this.configService.get<string>('SMTP_PASS'),
                },
            });

            // Verify connection
            await this.transporter.verify();
            this.logger.log('Email transporter initialized successfully');
        } catch (error) {
            this.logger.error('Email transporter verification failed:', error);
        }
    }

    /**
     * Send email verification
     */
    async sendEmailVerification(email: string, token: string, username: string): Promise<void> {
        try {
            const template = await this.loadTemplate('email-verification');
            const html = template({
                username,
                verificationUrl: `${this.configService.get('FE_DOMAIN')}/verify-email?token=${token}`,
                appName: 'APISportsGame'
            });

            await this.transporter.sendMail({
                from: this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
                to: email,
                subject: 'Verify Your Email Address',
                html,
            });

            this.logger.log(`Email verification sent to ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send email verification to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send password reset email
     */
    async sendPasswordReset(email: string, token: string, username: string): Promise<void> {
        try {
            const template = await this.loadTemplate('password-reset');
            const html = template({
                username,
                resetUrl: `${this.configService.get('FE_DOMAIN')}/reset-password?token=${token}`,
                appName: 'APISportsGame'
            });

            await this.transporter.sendMail({
                from: this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
                to: email,
                subject: 'Reset Your Password',
                html,
            });

            this.logger.log(`Password reset email sent to ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send password reset email to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send welcome email
     */
    async sendWelcomeEmail(email: string, username: string): Promise<void> {
        try {
            const template = await this.loadTemplate('welcome');
            const html = template({
                username,
                loginUrl: `${this.configService.get('FE_DOMAIN')}/login`,
                appName: 'APISportsGame'
            });

            await this.transporter.sendMail({
                from: this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
                to: email,
                subject: 'Welcome to APISportsGame!',
                html,
            });

            this.logger.log(`Welcome email sent to ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send welcome email to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send tier upgrade notification
     */
    async sendTierUpgradeNotification(email: string, username: string, newTier: string): Promise<void> {
        try {
            const template = await this.loadTemplate('tier-upgrade');
            const html = template({
                username,
                newTier,
                appName: 'APISportsGame'
            });

            await this.transporter.sendMail({
                from: this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
                to: email,
                subject: `Tier Upgraded to ${newTier}`,
                html,
            });

            this.logger.log(`Tier upgrade notification sent to ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send tier upgrade notification to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send API limit warning
     */
    async sendApiLimitWarning(email: string, username: string, usagePercentage: number): Promise<void> {
        try {
            const template = await this.loadTemplate('api-limit-warning');
            const html = template({
                username,
                usagePercentage,
                appName: 'APISportsGame'
            });

            await this.transporter.sendMail({
                from: this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
                to: email,
                subject: 'API Usage Warning',
                html,
            });

            this.logger.log(`API limit warning sent to ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send API limit warning to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Load email template
     */
    private async loadTemplate(templateName: string): Promise<HandlebarsTemplateDelegate> {
        try {
            const templatePath = path.join(__dirname, '../../core/templates', `${templateName}.hbs`);
            const templateContent = fs.readFileSync(templatePath, 'utf8');
            return handlebars.compile(templateContent);
        } catch (error) {
            this.logger.error(`Failed to load template ${templateName}:`, error);
            // Return a basic template as fallback
            return handlebars.compile('<p>{{username}}, please check your email for further instructions.</p>');
        }
    }
}
