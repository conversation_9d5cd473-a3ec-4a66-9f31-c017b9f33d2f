import {
    Controller,
    Post,
    Get,
    Put,
    Body,
    HttpCode,
    HttpStatus,
    UseGuards,
    Request,
    Logger,
    Ip,
    <PERSON><PERSON>,
    <PERSON>m,
    BadRequestException,
    NotFoundException
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBody,
    ApiBearerAuth,
    ApiParam,
} from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { RegisteredUserService } from '../services/registered-user.service';
import {
    RegisteredUserRegisterDto,
    RegisteredUserLoginDto,
    RegisteredUserAuthResponseDto,
    RegisteredUserProfileDto,
    EmailVerificationDto,
    ResendEmailVerificationDto,
    PasswordResetRequestDto,
    PasswordResetDto,
    UpdateProfileDto,
    ChangePasswordDto
} from '../dto/registered-user.dto';
import { DeviceInfoDto } from '../dto/auth.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { AuthRateLimitGuard } from '../guards/rate-limit.guard';
import { Public, CurrentUser } from '../decorators/auth.decorators';
import { LoginRateLimit, RegisterRateLimit, RefreshRateLimit } from '../decorators/rate-limit.decorators';
import { RegisteredUser } from '../entities/registered-user.entity';

/**
 * Registered User Controller
 * Handles end-user authentication and profile management
 */
@ApiTags('Registered Users')
@Controller('users')
@UseGuards(JwtAuthGuard, AuthRateLimitGuard)
export class RegisteredUserController {
    private readonly logger = new Logger(RegisteredUserController.name);

    constructor(
        private readonly authService: AuthService,
        private readonly registeredUserService: RegisteredUserService,
    ) { }

    /**
     * User registration
     * POST /users/register
     */
    @ApiOperation({
        summary: 'User Registration',
        description: 'Register a new user account. Email verification required. Rate limited to 3 attempts per 5 minutes per IP.'
    })
    @ApiResponse({
        status: 201,
        description: 'User registered successfully. Email verification required.',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Registration successful. Please check your email for verification.' },
                user: {
                    type: 'object',
                    properties: {
                        id: { type: 'number', example: 1 },
                        username: { type: 'string', example: 'john_doe' },
                        email: { type: 'string', example: '<EMAIL>' },
                        isEmailVerified: { type: 'boolean', example: false }
                    }
                }
            }
        }
    })
    @ApiResponse({ status: 400, description: 'Invalid input data' })
    @ApiResponse({ status: 409, description: 'Username or email already exists' })
    @ApiResponse({ status: 429, description: 'Too many registration attempts' })
    @Public()
    @RegisterRateLimit()
    @Post('register')
    @HttpCode(HttpStatus.CREATED)
    async register(
        @Body() registerDto: RegisteredUserRegisterDto,
        @Ip() ipAddress: string,
    ): Promise<{ message: string; user: { id: number; username: string; email: string; isEmailVerified: boolean } }> {
        this.logger.log(`Registration attempt for user: ${registerDto.username} from IP: ${ipAddress}`);

        const user = await this.registeredUserService.register(registerDto, ipAddress);

        this.logger.log(`User registered successfully: ${user.username} (ID: ${user.id})`);

        return {
            message: 'Registration successful. Please check your email for verification.',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                isEmailVerified: user.isEmailVerified
            }
        };
    }

    /**
     * User login
     * POST /users/login
     */
    @ApiOperation({
        summary: 'User Login',
        description: 'Authenticate user and receive JWT tokens. Email verification required. Rate limited to 5 attempts per minute per IP.'
    })
    @ApiResponse({
        status: 200,
        description: 'Login successful',
        type: RegisteredUserAuthResponseDto
    })
    @ApiResponse({ status: 401, description: 'Invalid credentials or email not verified' })
    @ApiResponse({ status: 429, description: 'Too many login attempts' })
    @Public()
    @LoginRateLimit()
    @Post('login')
    @HttpCode(HttpStatus.OK)
    async login(
        @Body() loginDto: RegisteredUserLoginDto,
        @Ip() ipAddress: string,
        @Headers('user-agent') userAgent: string,
    ): Promise<RegisteredUserAuthResponseDto> {
        this.logger.log(`Login attempt for user: ${loginDto.usernameOrEmail} from IP: ${ipAddress}`);

        // Create device info
        const deviceInfo: DeviceInfoDto = {
            ipAddress,
            userAgent,
            deviceInfo: `${userAgent?.split(' ')[0] || 'Unknown'} from ${ipAddress}`,
        };

        // Authenticate user
        const result = await this.authService.loginRegisteredUser(loginDto, deviceInfo);

        this.logger.log(`User ${result.user.username} logged in successfully`);

        return result;
    }

    /**
     * Email verification
     * POST /users/verify-email
     */
    @ApiOperation({
        summary: 'Verify Email',
        description: 'Verify user email with verification token'
    })
    @ApiResponse({
        status: 200,
        description: 'Email verified successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Email verified successfully' },
                user: { $ref: '#/components/schemas/RegisteredUserProfileDto' }
            }
        }
    })
    @ApiResponse({ status: 400, description: 'Invalid or expired verification token' })
    @Public()
    @Post('verify-email')
    @HttpCode(HttpStatus.OK)
    async verifyEmail(
        @Body() verificationDto: EmailVerificationDto,
    ): Promise<{ message: string; user: RegisteredUserProfileDto }> {
        this.logger.debug(`Email verification attempt with token: ${verificationDto.token.substring(0, 8)}...`);

        const user = await this.registeredUserService.verifyEmail(verificationDto.token);
        const userProfile = this.registeredUserService.toProfileDto(user);

        this.logger.log(`Email verified successfully for user: ${user.username}`);

        return {
            message: 'Email verified successfully',
            user: userProfile
        };
    }

    /**
     * Resend email verification
     * POST /users/resend-verification
     */
    @ApiOperation({
        summary: 'Resend Email Verification',
        description: 'Resend email verification token to user email'
    })
    @ApiResponse({
        status: 200,
        description: 'Verification email sent',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Verification email sent successfully' }
            }
        }
    })
    @ApiResponse({ status: 400, description: 'Email already verified' })
    @ApiResponse({ status: 404, description: 'User not found' })
    @Public()
    @RefreshRateLimit()
    @Post('resend-verification')
    @HttpCode(HttpStatus.OK)
    async resendVerification(
        @Body() resendDto: ResendEmailVerificationDto,
    ): Promise<{ message: string }> {
        this.logger.debug(`Resend verification request for email: ${resendDto.email}`);

        await this.registeredUserService.generateEmailVerificationToken(resendDto.email);

        this.logger.log(`Verification email resent to: ${resendDto.email}`);

        return {
            message: 'Verification email sent successfully'
        };
    }

    /**
     * Request password reset
     * POST /users/forgot-password
     */
    @ApiOperation({
        summary: 'Request Password Reset',
        description: 'Request password reset token via email'
    })
    @ApiResponse({
        status: 200,
        description: 'Password reset email sent (if email exists)',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'If the email exists, a password reset link has been sent' }
            }
        }
    })
    @Public()
    @RefreshRateLimit()
    @Post('forgot-password')
    @HttpCode(HttpStatus.OK)
    async forgotPassword(
        @Body() resetRequestDto: PasswordResetRequestDto,
    ): Promise<{ message: string }> {
        this.logger.debug(`Password reset request for email: ${resetRequestDto.email}`);

        await this.registeredUserService.generatePasswordResetToken(resetRequestDto.email);

        this.logger.log(`Password reset request processed for: ${resetRequestDto.email}`);

        return {
            message: 'If the email exists, a password reset link has been sent'
        };
    }

    /**
     * Reset password
     * POST /users/reset-password
     */
    @ApiOperation({
        summary: 'Reset Password',
        description: 'Reset password using reset token'
    })
    @ApiResponse({
        status: 200,
        description: 'Password reset successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Password reset successfully' }
            }
        }
    })
    @ApiResponse({ status: 400, description: 'Invalid or expired reset token' })
    @Public()
    @Post('reset-password')
    @HttpCode(HttpStatus.OK)
    async resetPassword(
        @Body() resetDto: PasswordResetDto,
    ): Promise<{ message: string }> {
        this.logger.debug(`Password reset attempt with token: ${resetDto.token.substring(0, 8)}...`);

        await this.registeredUserService.resetPassword(resetDto.token, resetDto.newPassword);

        this.logger.log(`Password reset completed successfully`);

        return {
            message: 'Password reset successfully'
        };
    }

    /**
     * Get user profile
     * GET /users/profile
     */
    @ApiOperation({
        summary: 'Get User Profile',
        description: 'Get current user profile information. Requires authentication.'
    })
    @ApiResponse({
        status: 200,
        description: 'User profile retrieved successfully',
        type: RegisteredUserProfileDto
    })
    @ApiResponse({ status: 401, description: 'Unauthorized' })
    @ApiBearerAuth()
    @Get('profile')
    async getProfile(
        @CurrentUser() user: RegisteredUser,
    ): Promise<RegisteredUserProfileDto> {
        this.logger.debug(`Profile request for user: ${user.username}`);

        return this.registeredUserService.toProfileDto(user);
    }

    /**
     * Update user profile
     * PUT /users/profile
     */
    @ApiOperation({
        summary: 'Update User Profile',
        description: 'Update user profile information. Requires authentication.'
    })
    @ApiResponse({
        status: 200,
        description: 'Profile updated successfully',
        type: RegisteredUserProfileDto
    })
    @ApiResponse({ status: 400, description: 'Invalid input data' })
    @ApiResponse({ status: 401, description: 'Unauthorized' })
    @ApiBearerAuth()
    @Put('profile')
    async updateProfile(
        @CurrentUser() user: RegisteredUser,
        @Body() updateDto: UpdateProfileDto,
    ): Promise<RegisteredUserProfileDto> {
        this.logger.log(`Profile update request for user: ${user.username}`);

        const updatedUser = await this.registeredUserService.updateProfile(user.id, updateDto);

        this.logger.log(`Profile updated successfully for user: ${user.username}`);

        return this.registeredUserService.toProfileDto(updatedUser);
    }

    /**
     * Change password
     * POST /users/change-password
     */
    @ApiOperation({
        summary: 'Change Password',
        description: 'Change user password. Requires current password verification.'
    })
    @ApiResponse({
        status: 200,
        description: 'Password changed successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Password changed successfully' }
            }
        }
    })
    @ApiResponse({ status: 400, description: 'Invalid current password' })
    @ApiResponse({ status: 401, description: 'Unauthorized' })
    @ApiBearerAuth()
    @Post('change-password')
    @HttpCode(HttpStatus.OK)
    async changePassword(
        @CurrentUser() user: RegisteredUser,
        @Body() changePasswordDto: ChangePasswordDto,
    ): Promise<{ message: string }> {
        this.logger.log(`Password change request for user: ${user.username}`);

        await this.registeredUserService.changePassword(user.id, changePasswordDto);

        this.logger.log(`Password changed successfully for user: ${user.username}`);

        return {
            message: 'Password changed successfully'
        };
    }

    /**
     * Get API usage statistics
     * GET /users/api-usage
     */
    @ApiOperation({
        summary: 'Get API Usage Statistics',
        description: 'Get current month API usage statistics for the user'
    })
    @ApiResponse({
        status: 200,
        description: 'API usage statistics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                tier: { type: 'string', example: 'premium', enum: ['free', 'premium', 'enterprise'] },
                apiCallsUsed: { type: 'number', example: 150 },
                apiCallsLimit: { type: 'number', example: 10000, nullable: true },
                apiCallsRemaining: { type: 'number', example: 9850, nullable: true },
                lastApiCallAt: { type: 'string', format: 'date-time', nullable: true },
                resetDate: { type: 'string', format: 'date-time', example: '2024-02-01T00:00:00.000Z' }
            }
        }
    })
    @ApiResponse({ status: 401, description: 'Unauthorized' })
    @ApiBearerAuth()
    @Get('api-usage')
    async getApiUsage(
        @CurrentUser() user: RegisteredUser,
    ): Promise<{
        tier: string;
        apiCallsUsed: number;
        apiCallsLimit: number | null;
        apiCallsRemaining: number | null;
        lastApiCallAt: Date | null;
        resetDate: Date;
    }> {
        this.logger.debug(`API usage request for user: ${user.username}`);

        // Calculate next reset date (first day of next month)
        const now = new Date();
        const resetDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);

        return {
            tier: user.tier,
            apiCallsUsed: user.apiCallsUsed,
            apiCallsLimit: user.apiCallsLimit,
            apiCallsRemaining: user.getApiCallsRemaining(),
            lastApiCallAt: user.lastApiCallAt,
            resetDate
        };
    }
}
