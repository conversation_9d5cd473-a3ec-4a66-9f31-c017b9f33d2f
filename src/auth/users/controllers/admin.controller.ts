import { 
    Controller, 
    Get, 
    Post, 
    Patch, 
    Param, 
    Body, 
    Query, 
    UseGuards, 
    HttpCode, 
    HttpStatus,
    ParseIntPipe 
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiParam,
    ApiQuery,
    ApiBody,
    ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { AdminOnly } from '../decorators/auth.decorators';
import { TierManagementService } from '../services/tier-management.service';
import { RegisteredUserService } from '../services/registered-user.service';
import { RegisteredUser } from '../entities/registered-user.entity';
import { RegisteredUserTier } from '../types/auth.types';

/**
 * Admin Controller
 * Handles administrative operations for user and tier management
 */
@ApiTags('Admin - User Management')
@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@AdminOnly()
@ApiBearerAuth()
export class AdminController {
    constructor(
        private readonly tierManagementService: TierManagementService,
        private readonly registeredUserService: RegisteredUserService,
    ) {}

    @ApiOperation({
        summary: 'Get Tier Statistics',
        description: 'Get statistics about user distribution across tiers'
    })
    @ApiResponse({
        status: 200,
        description: 'Tier statistics retrieved successfully',
        example: {
            free: 150,
            premium: 45,
            enterprise: 12,
            total: 207
        }
    })
    @Get('tiers/statistics')
    async getTierStatistics(): Promise<{
        free: number;
        premium: number;
        enterprise: number;
        total: number;
    }> {
        return this.tierManagementService.getTierStatistics();
    }

    @ApiOperation({
        summary: 'Get Users Approaching API Limits',
        description: 'Get list of users who are approaching their API usage limits'
    })
    @ApiQuery({
        name: 'threshold',
        required: false,
        type: Number,
        description: 'Usage percentage threshold (default: 80)',
        example: 80
    })
    @ApiResponse({
        status: 200,
        description: 'Users approaching limits retrieved successfully'
    })
    @Get('users/approaching-limits')
    async getUsersApproachingLimits(
        @Query('threshold') threshold?: number
    ): Promise<RegisteredUser[]> {
        return this.tierManagementService.getUsersApproachingLimits(threshold);
    }

    @ApiOperation({
        summary: 'Upgrade User Tier',
        description: 'Upgrade a user to a higher tier with optional subscription duration'
    })
    @ApiParam({
        name: 'userId',
        type: 'number',
        description: 'User ID to upgrade'
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                newTier: {
                    type: 'string',
                    enum: ['premium', 'enterprise'],
                    description: 'New tier to upgrade to'
                },
                subscriptionMonths: {
                    type: 'number',
                    description: 'Subscription duration in months (for premium/enterprise)',
                    example: 12
                }
            },
            required: ['newTier']
        }
    })
    @ApiResponse({
        status: 200,
        description: 'User tier upgraded successfully'
    })
    @ApiResponse({
        status: 404,
        description: 'User not found'
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid tier upgrade'
    })
    @Post('users/:userId/upgrade-tier')
    async upgradeTier(
        @Param('userId', ParseIntPipe) userId: number,
        @Body() body: { newTier: RegisteredUserTier; subscriptionMonths?: number }
    ): Promise<{ message: string; user: any }> {
        const user = await this.tierManagementService.upgradeTier(
            userId, 
            body.newTier, 
            body.subscriptionMonths
        );

        return {
            message: `User tier upgraded to ${body.newTier} successfully`,
            user: this.registeredUserService.toProfileDto(user)
        };
    }

    @ApiOperation({
        summary: 'Downgrade User Tier',
        description: 'Downgrade a user to a lower tier'
    })
    @ApiParam({
        name: 'userId',
        type: 'number',
        description: 'User ID to downgrade'
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                newTier: {
                    type: 'string',
                    enum: ['free', 'premium'],
                    description: 'New tier to downgrade to'
                }
            },
            required: ['newTier']
        }
    })
    @ApiResponse({
        status: 200,
        description: 'User tier downgraded successfully'
    })
    @Post('users/:userId/downgrade-tier')
    async downgradeTier(
        @Param('userId', ParseIntPipe) userId: number,
        @Body() body: { newTier: RegisteredUserTier }
    ): Promise<{ message: string; user: any }> {
        const user = await this.tierManagementService.downgradeTier(userId, body.newTier);

        return {
            message: `User tier downgraded to ${body.newTier} successfully`,
            user: this.registeredUserService.toProfileDto(user)
        };
    }

    @ApiOperation({
        summary: 'Extend User Subscription',
        description: 'Extend a user subscription by additional months'
    })
    @ApiParam({
        name: 'userId',
        type: 'number',
        description: 'User ID to extend subscription'
    })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                additionalMonths: {
                    type: 'number',
                    description: 'Additional months to extend',
                    example: 6
                }
            },
            required: ['additionalMonths']
        }
    })
    @ApiResponse({
        status: 200,
        description: 'Subscription extended successfully'
    })
    @Post('users/:userId/extend-subscription')
    async extendSubscription(
        @Param('userId', ParseIntPipe) userId: number,
        @Body() body: { additionalMonths: number }
    ): Promise<{ message: string; user: any }> {
        const user = await this.tierManagementService.extendSubscription(
            userId, 
            body.additionalMonths
        );

        return {
            message: `Subscription extended by ${body.additionalMonths} months`,
            user: this.registeredUserService.toProfileDto(user)
        };
    }

    @ApiOperation({
        summary: 'Reset Monthly API Usage',
        description: 'Reset API usage counters for all users (typically run monthly)'
    })
    @ApiResponse({
        status: 200,
        description: 'API usage reset successfully'
    })
    @Post('reset-api-usage')
    @HttpCode(HttpStatus.OK)
    async resetMonthlyApiUsage(): Promise<{ message: string }> {
        await this.tierManagementService.resetMonthlyApiUsage();
        return { message: 'Monthly API usage reset for all users' };
    }

    @ApiOperation({
        summary: 'Check API Usage Warnings',
        description: 'Check and send API usage warnings to users approaching limits'
    })
    @ApiResponse({
        status: 200,
        description: 'API usage warnings checked and sent'
    })
    @Post('check-usage-warnings')
    @HttpCode(HttpStatus.OK)
    async checkApiUsageWarnings(): Promise<{ message: string }> {
        await this.tierManagementService.checkApiUsageWarnings();
        return { message: 'API usage warnings checked and sent' };
    }

    @ApiOperation({
        summary: 'Get User Subscription Info',
        description: 'Get detailed subscription information for a user'
    })
    @ApiParam({
        name: 'userId',
        type: 'number',
        description: 'User ID to get subscription info'
    })
    @ApiResponse({
        status: 200,
        description: 'Subscription info retrieved successfully'
    })
    @Get('users/:userId/subscription')
    async getSubscriptionInfo(
        @Param('userId', ParseIntPipe) userId: number
    ): Promise<{
        tier: string;
        isActive: boolean;
        startDate: Date | null;
        endDate: Date | null;
        daysRemaining: number | null;
    }> {
        return this.tierManagementService.getSubscriptionInfo(userId);
    }

    @ApiOperation({
        summary: 'Get All Registered Users',
        description: 'Get paginated list of all registered users with filtering'
    })
    @ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number',
        example: 1
    })
    @ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page',
        example: 20
    })
    @ApiQuery({
        name: 'tier',
        required: false,
        type: String,
        description: 'Filter by tier',
        enum: ['free', 'premium', 'enterprise']
    })
    @ApiQuery({
        name: 'isActive',
        required: false,
        type: Boolean,
        description: 'Filter by active status'
    })
    @ApiResponse({
        status: 200,
        description: 'Users retrieved successfully'
    })
    @Get('users')
    async getAllUsers(
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 20,
        @Query('tier') tier?: RegisteredUserTier,
        @Query('isActive') isActive?: boolean
    ): Promise<{
        data: any[];
        meta: {
            totalItems: number;
            totalPages: number;
            currentPage: number;
            limit: number;
        };
    }> {
        return this.registeredUserService.getAllUsers({
            page,
            limit,
            tier,
            isActive
        });
    }
}
