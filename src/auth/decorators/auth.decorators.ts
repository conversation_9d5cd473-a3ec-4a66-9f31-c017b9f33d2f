import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { SystemRole, RegisteredUserTier } from '../types/auth.types';
import { SystemUser } from '../entities/system-user.entity';
import { RegisteredUser } from '../entities/registered-user.entity';

/**
 * Public decorator - marks routes as public (no authentication required)
 */
export const Public = () => SetMetadata('isPublic', true);

/**
 * Roles decorator - specifies required roles for accessing endpoint
 */
export const Roles = (...roles: SystemRole[]) => SetMetadata('roles', roles);

/**
 * CurrentUser decorator - extracts current user from request
 * Supports both SystemUser and RegisteredUser
 */
export const CurrentUser = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): SystemUser | RegisteredUser => {
        const request = ctx.switchToHttp().getRequest();
        return request.user;
    },
);

/**
 * Tiers decorator - specifies required tiers for registered users
 */
export const RequireTiers = (...tiers: RegisteredUserTier[]) => SetMetadata('tiers', tiers);

/**
 * Email verification required decorator
 */
export const RequireEmailVerification = () => SetMetadata('requireEmailVerification', true);

/**
 * Admin Only decorator - shorthand for admin role requirement
 */
export const AdminOnly = () => Roles(SystemRole.ADMIN);

/**
 * Editor Plus decorator - allows admin and editor roles
 */
export const EditorPlus = () => Roles(SystemRole.ADMIN, SystemRole.EDITOR);

/**
 * Moderator Plus decorator - allows admin and moderator roles
 */
export const ModeratorPlus = () => Roles(SystemRole.ADMIN, SystemRole.MODERATOR);
