import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegisteredUser } from '../entities/registered-user.entity';
import { EmailService } from './email.service';
import { RegisteredUserTier } from '../types/auth.types';

/**
 * Tier Management Service
 * Handles tier upgrades, downgrades, and API usage monitoring
 */
@Injectable()
export class TierManagementService {
    private readonly logger = new Logger(TierManagementService.name);

    constructor(
        @InjectRepository(RegisteredUser)
        private readonly registeredUserRepository: Repository<RegisteredUser>,
        private readonly emailService: EmailService,
    ) {}

    /**
     * Upgrade user tier
     */
    async upgradeTier(userId: number, newTier: RegisteredUserTier, subscriptionMonths?: number): Promise<RegisteredUser> {
        const user = await this.registeredUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Validate tier upgrade path
        if (!this.isValidTierUpgrade(user.tier, newTier)) {
            throw new BadRequestException(`Invalid tier upgrade from ${user.tier} to ${newTier}`);
        }

        const oldTier = user.tier;
        user.tier = newTier;

        // Set subscription dates for premium/enterprise
        if (newTier !== 'free' && subscriptionMonths) {
            user.subscriptionStartDate = new Date();
            user.subscriptionEndDate = new Date();
            user.subscriptionEndDate.setMonth(user.subscriptionEndDate.getMonth() + subscriptionMonths);
        }

        // Update API limits
        user.apiCallsLimit = this.getApiLimitForTier(newTier);
        
        // Reset API usage for new billing cycle
        user.apiCallsUsed = 0;

        const updatedUser = await this.registeredUserRepository.save(user);
        this.logger.log(`User ${user.username} upgraded from ${oldTier} to ${newTier}`);

        // Send tier upgrade notification
        try {
            await this.emailService.sendTierUpgradeNotification(user.email, user.username, newTier);
        } catch (error) {
            this.logger.error(`Failed to send tier upgrade notification to ${user.email}:`, error);
        }

        return updatedUser;
    }

    /**
     * Downgrade user tier
     */
    async downgradeTier(userId: number, newTier: RegisteredUserTier): Promise<RegisteredUser> {
        const user = await this.registeredUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException('User not found');
        }

        const oldTier = user.tier;
        user.tier = newTier;

        // Clear subscription dates for free tier
        if (newTier === 'free') {
            user.subscriptionStartDate = null;
            user.subscriptionEndDate = null;
        }

        // Update API limits
        user.apiCallsLimit = this.getApiLimitForTier(newTier);

        // If new limit is lower than current usage, reset usage
        if (user.apiCallsLimit && user.apiCallsUsed > user.apiCallsLimit) {
            user.apiCallsUsed = 0;
        }

        const updatedUser = await this.registeredUserRepository.save(user);
        this.logger.log(`User ${user.username} downgraded from ${oldTier} to ${newTier}`);

        return updatedUser;
    }

    /**
     * Check and send API usage warnings
     */
    async checkApiUsageWarnings(): Promise<void> {
        const users = await this.registeredUserRepository.find({
            where: { isActive: true, apiCallsLimit: null } // Users with limits
        });

        for (const user of users) {
            if (user.apiCallsLimit && user.apiCallsUsed > 0) {
                const usagePercentage = (user.apiCallsUsed / user.apiCallsLimit) * 100;

                // Send warning at 80% and 95% usage
                if (usagePercentage >= 80 && usagePercentage < 95) {
                    await this.sendUsageWarning(user, usagePercentage);
                } else if (usagePercentage >= 95) {
                    await this.sendUsageWarning(user, usagePercentage);
                }
            }
        }
    }

    /**
     * Reset monthly API usage for all users
     */
    async resetMonthlyApiUsage(): Promise<void> {
        await this.registeredUserRepository.update(
            { isActive: true },
            { 
                apiCallsUsed: 0,
                lastApiCallAt: null
            }
        );

        this.logger.log('Monthly API usage reset for all users');
    }

    /**
     * Get users approaching their API limits
     */
    async getUsersApproachingLimits(threshold: number = 80): Promise<RegisteredUser[]> {
        const users = await this.registeredUserRepository.find({
            where: { isActive: true }
        });

        return users.filter(user => {
            if (!user.apiCallsLimit || user.apiCallsUsed === 0) return false;
            const usagePercentage = (user.apiCallsUsed / user.apiCallsLimit) * 100;
            return usagePercentage >= threshold;
        });
    }

    /**
     * Get tier statistics
     */
    async getTierStatistics(): Promise<{
        free: number;
        premium: number;
        enterprise: number;
        total: number;
    }> {
        const [free, premium, enterprise, total] = await Promise.all([
            this.registeredUserRepository.count({ where: { tier: 'free', isActive: true } }),
            this.registeredUserRepository.count({ where: { tier: 'premium', isActive: true } }),
            this.registeredUserRepository.count({ where: { tier: 'enterprise', isActive: true } }),
            this.registeredUserRepository.count({ where: { isActive: true } }),
        ]);

        return { free, premium, enterprise, total };
    }

    /**
     * Validate tier upgrade path
     */
    private isValidTierUpgrade(currentTier: string, newTier: string): boolean {
        const tierHierarchy = { free: 0, premium: 1, enterprise: 2 };
        return tierHierarchy[newTier] > tierHierarchy[currentTier];
    }

    /**
     * Get API limit for tier
     */
    private getApiLimitForTier(tier: RegisteredUserTier): number | null {
        switch (tier) {
            case 'free':
                return 100; // 100 calls per month
            case 'premium':
                return 10000; // 10,000 calls per month
            case 'enterprise':
                return null; // Unlimited
            default:
                return 100;
        }
    }

    /**
     * Send usage warning email
     */
    private async sendUsageWarning(user: RegisteredUser, usagePercentage: number): Promise<void> {
        try {
            await this.emailService.sendApiLimitWarning(user.email, user.username, Math.round(usagePercentage));
            this.logger.log(`API usage warning sent to ${user.email} (${Math.round(usagePercentage)}% usage)`);
        } catch (error) {
            this.logger.error(`Failed to send API usage warning to ${user.email}:`, error);
        }
    }

    /**
     * Check if user has active subscription
     */
    async hasActiveSubscription(userId: number): Promise<boolean> {
        const user = await this.registeredUserRepository.findOne({ where: { id: userId } });
        if (!user) return false;

        if (user.tier === 'free') return true; // Free tier doesn't need subscription

        if (!user.subscriptionEndDate) return false;

        return user.subscriptionEndDate > new Date();
    }

    /**
     * Get subscription info
     */
    async getSubscriptionInfo(userId: number): Promise<{
        tier: string;
        isActive: boolean;
        startDate: Date | null;
        endDate: Date | null;
        daysRemaining: number | null;
    }> {
        const user = await this.registeredUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException('User not found');
        }

        const isActive = await this.hasActiveSubscription(userId);
        let daysRemaining: number | null = null;

        if (user.subscriptionEndDate && isActive) {
            const now = new Date();
            const timeDiff = user.subscriptionEndDate.getTime() - now.getTime();
            daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24));
        }

        return {
            tier: user.tier,
            isActive,
            startDate: user.subscriptionStartDate,
            endDate: user.subscriptionEndDate,
            daysRemaining,
        };
    }

    /**
     * Extend subscription
     */
    async extendSubscription(userId: number, additionalMonths: number): Promise<RegisteredUser> {
        const user = await this.registeredUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException('User not found');
        }

        if (user.tier === 'free') {
            throw new BadRequestException('Free tier users do not have subscriptions');
        }

        // Extend from current end date or now, whichever is later
        const baseDate = user.subscriptionEndDate && user.subscriptionEndDate > new Date() 
            ? user.subscriptionEndDate 
            : new Date();

        user.subscriptionEndDate = new Date(baseDate);
        user.subscriptionEndDate.setMonth(user.subscriptionEndDate.getMonth() + additionalMonths);

        const updatedUser = await this.registeredUserRepository.save(user);
        this.logger.log(`Subscription extended for user ${user.username} by ${additionalMonths} months`);

        return updatedUser;
    }
}
