import { Injectable, UnauthorizedException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { SystemUser } from '../entities/system-user.entity';
import { RegisteredUser } from '../entities/registered-user.entity';
import { RefreshToken } from '../entities/refresh-token.entity';
import { SystemUserLoginDto, SystemUserCreateDto, TokenPairDto, DeviceInfoDto } from '../dto/auth.dto';
import { RegisteredUserLoginDto, RegisteredUserAuthResponseDto } from '../dto/registered-user.dto';
import { SystemUserJwtPayload, RegisteredUserJwtPayload, UserType, SystemRole, RegisteredUserTier } from '../types/auth.types';
import { AuditLogService } from './audit-log.service';
import { RegisteredUserService } from './registered-user.service';

/**
 * Authentication Service
 * Handles login, registration, token management for system users
 */
@Injectable()
export class AuthService {
    private readonly logger = new Logger(AuthService.name);

    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        @InjectRepository(RegisteredUser)
        private registeredUserRepository: Repository<RegisteredUser>,
        @InjectRepository(RefreshToken)
        private refreshTokenRepository: Repository<RefreshToken>,
        private jwtService: JwtService,
        private configService: ConfigService,
        private auditLogService: AuditLogService,
        private registeredUserService: RegisteredUserService,
    ) { }

    /**
     * System user login
     */
    async loginSystemUser(loginDto: SystemUserLoginDto, deviceInfo?: DeviceInfoDto): Promise<TokenPairDto> {
        // Find user by username
        const user = await this.systemUserRepository.findOne({
            where: { username: loginDto.username, isActive: true }
        });

        if (!user) {
            // Log failed login attempt
            await this.auditLogService.logFailedLogin(
                loginDto.username,
                deviceInfo?.ipAddress,
                deviceInfo?.userAgent,
                'User not found or inactive'
            );
            throw new UnauthorizedException('Invalid credentials');
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(loginDto.password, user.passwordHash);
        if (!isPasswordValid) {
            // Log failed login attempt
            await this.auditLogService.logFailedLogin(
                loginDto.username,
                deviceInfo?.ipAddress,
                deviceInfo?.userAgent,
                'Invalid password'
            );
            throw new UnauthorizedException('Invalid credentials');
        }

        // Generate token pair
        const tokens = await this.generateTokenPair(user);

        // Store refresh token
        await this.storeRefreshToken(tokens.refreshToken, user.id, 'system', deviceInfo);

        // Update last login
        user.lastLoginAt = new Date();
        await this.systemUserRepository.save(user);

        // Log successful login
        await this.auditLogService.logLogin(user, deviceInfo?.ipAddress, deviceInfo?.userAgent);

        this.logger.log(`System user ${user.username} logged in successfully`);
        return tokens;
    }

    /**
     * Registered user login
     */
    async loginRegisteredUser(loginDto: RegisteredUserLoginDto, deviceInfo?: DeviceInfoDto): Promise<RegisteredUserAuthResponseDto> {
        this.logger.debug(`Login attempt for registered user: ${loginDto.usernameOrEmail}`);

        // Find user by username or email
        const user = await this.registeredUserService.findByUsernameOrEmail(loginDto.usernameOrEmail);

        if (!user || !user.isActive) {
            this.logger.warn(`Login failed for: ${loginDto.usernameOrEmail} - User not found or inactive`);
            throw new UnauthorizedException('Invalid credentials');
        }

        // Verify password
        const isPasswordValid = await this.registeredUserService.verifyPassword(user, loginDto.password);
        if (!isPasswordValid) {
            this.logger.warn(`Login failed for: ${loginDto.usernameOrEmail} - Invalid password`);
            throw new UnauthorizedException('Invalid credentials');
        }

        // Check if email is verified (optional: can be enforced based on business rules)
        if (!user.isEmailVerified) {
            this.logger.warn(`Login attempt with unverified email: ${user.email}`);
            throw new UnauthorizedException('Please verify your email before logging in');
        }

        // Generate token pair
        const tokens = await this.generateRegisteredUserTokenPair(user);

        // Store refresh token
        await this.storeRefreshToken(tokens.refreshToken, user.id, 'registered', deviceInfo);

        // Update last login
        user.lastLoginAt = new Date();
        if (deviceInfo?.ipAddress) {
            user.lastLoginIp = deviceInfo.ipAddress;
        }
        await this.registeredUserRepository.save(user);

        this.logger.log(`Registered user ${user.username} logged in successfully`);

        return {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            user: this.registeredUserService.toProfileDto(user)
        };
    }

    /**
     * Create system user (admin registration)
     */
    async createSystemUser(createDto: SystemUserCreateDto, createdBy?: number): Promise<SystemUser> {
        // Check if username already exists
        const existingUsername = await this.systemUserRepository.findOne({
            where: { username: createDto.username }
        });
        if (existingUsername) {
            throw new ConflictException('Username already exists');
        }

        // Check if email already exists
        const existingEmail = await this.systemUserRepository.findOne({
            where: { email: createDto.email }
        });
        if (existingEmail) {
            throw new ConflictException('Email already exists');
        }

        // Hash password
        const passwordHash = await bcrypt.hash(createDto.password, 12);

        // Create user
        const user = this.systemUserRepository.create({
            username: createDto.username,
            email: createDto.email,
            passwordHash,
            fullName: createDto.fullName,
            role: createDto.role,
            createdBy,
        });

        const savedUser = await this.systemUserRepository.save(user);
        this.logger.log(`System user ${savedUser.username} created with role ${savedUser.role}`);

        return savedUser;
    }

    /**
     * Refresh access token
     */
    async refreshAccessToken(refreshTokenString: string): Promise<{ accessToken: string }> {
        // Find refresh token in database
        const refreshToken = await this.refreshTokenRepository.findOne({
            where: { token: refreshTokenString, isRevoked: false },
            relations: ['systemUser']
        });

        if (!refreshToken || !refreshToken.isValid()) {
            throw new UnauthorizedException('Invalid or expired refresh token');
        }

        // Generate new access token
        const accessToken = await this.generateAccessToken(refreshToken.systemUser);

        this.logger.debug(`Access token refreshed for user ${refreshToken.systemUser.username}`);
        return { accessToken };
    }

    /**
     * Logout user (revoke refresh token)
     */
    async logout(refreshTokenString: string): Promise<void> {
        const refreshToken = await this.refreshTokenRepository.findOne({
            where: { token: refreshTokenString }
        });

        if (refreshToken) {
            refreshToken.revoke();
            await this.refreshTokenRepository.save(refreshToken);
            this.logger.log(`Refresh token revoked for user ${refreshToken.userId}`);
        }
    }

    /**
     * Logout from all devices (revoke all refresh tokens for user)
     */
    async logoutFromAllDevices(userId: number): Promise<void> {
        await this.refreshTokenRepository.update(
            { userId, isRevoked: false },
            { isRevoked: true, revokedAt: new Date() }
        );

        this.logger.log(`All refresh tokens revoked for user ${userId}`);
    }

    /**
     * Generate access and refresh token pair for system users
     */
    private async generateTokenPair(user: SystemUser): Promise<TokenPairDto> {
        const accessToken = await this.generateAccessToken(user);
        const refreshToken = await this.generateRefreshToken(user);

        return { accessToken, refreshToken };
    }

    /**
     * Generate access and refresh token pair for registered users
     */
    private async generateRegisteredUserTokenPair(user: RegisteredUser): Promise<TokenPairDto> {
        const accessToken = await this.generateRegisteredUserAccessToken(user);
        const refreshToken = await this.generateRegisteredUserRefreshToken(user);

        return { accessToken, refreshToken };
    }

    /**
     * Generate access token for system users (short-lived)
     */
    private async generateAccessToken(user: SystemUser): Promise<string> {
        const payload: SystemUserJwtPayload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            role: user.role as SystemRole,
            userType: UserType.SYSTEM,
        };

        return this.jwtService.signAsync(payload, {
            expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
        });
    }

    /**
     * Generate access token for registered users (short-lived)
     */
    private async generateRegisteredUserAccessToken(user: RegisteredUser): Promise<string> {
        const payload: RegisteredUserJwtPayload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            tier: user.tier as RegisteredUserTier,
            userType: UserType.REGISTERED,
            isEmailVerified: user.isEmailVerified,
        };

        return this.jwtService.signAsync(payload, {
            expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
        });
    }

    /**
     * Generate refresh token for system users (long-lived)
     */
    private async generateRefreshToken(user: SystemUser): Promise<string> {
        const payload = {
            sub: user.id,
            type: 'refresh',
            userType: UserType.SYSTEM,
        };

        return this.jwtService.signAsync(payload, {
            secret: this.configService.get<string>('JWT_REFRESH_SECRET', 'default-refresh-secret'),
            expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d'),
        });
    }

    /**
     * Generate refresh token for registered users (long-lived)
     */
    private async generateRegisteredUserRefreshToken(user: RegisteredUser): Promise<string> {
        const payload = {
            sub: user.id,
            type: 'refresh',
            userType: UserType.REGISTERED,
        };

        return this.jwtService.signAsync(payload, {
            secret: this.configService.get<string>('JWT_REFRESH_SECRET', 'default-refresh-secret'),
            expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRES_IN', '7d'),
        });
    }

    /**
     * Store refresh token in database
     */
    private async storeRefreshToken(
        token: string,
        userId: number,
        userType: 'system' | 'registered',
        deviceInfo?: DeviceInfoDto
    ): Promise<void> {
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

        const refreshToken = this.refreshTokenRepository.create({
            token,
            userId,
            userType,
            deviceInfo: deviceInfo?.deviceInfo,
            ipAddress: deviceInfo?.ipAddress,
            userAgent: deviceInfo?.userAgent,
            expiresAt,
        });

        await this.refreshTokenRepository.save(refreshToken);
    }

    /**
     * Clean up expired refresh tokens
     */
    async cleanupExpiredTokens(): Promise<void> {
        const result = await this.refreshTokenRepository.delete({
            expiresAt: { $lt: new Date() } as any
        });

        this.logger.log(`Cleaned up ${result.affected} expired refresh tokens`);
    }
}
