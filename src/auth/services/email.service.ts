import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as handlebars from 'handlebars';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Email Service
 * Handles all email communications for the application
 */
@Injectable()
export class EmailService {
    private readonly logger = new Logger(EmailService.name);
    private transporter: nodemailer.Transporter;

    constructor(private readonly configService: ConfigService) {
        this.initializeTransporter();
    }

    /**
     * Initialize email transporter
     */
    private initializeTransporter(): void {
        const emailConfig = {
            host: this.configService.get<string>('SMTP_HOST', 'localhost'),
            port: this.configService.get<number>('SMTP_PORT', 587),
            secure: this.configService.get<boolean>('SMTP_SECURE', false),
            auth: {
                user: this.configService.get<string>('SMTP_USER'),
                pass: this.configService.get<string>('SMTP_PASS'),
            },
        };

        this.transporter = nodemailer.createTransport(emailConfig);

        // Verify connection
        this.transporter.verify((error, success) => {
            if (error) {
                this.logger.error('Email transporter verification failed:', error);
            } else {
                this.logger.log('Email transporter is ready to send emails');
            }
        });
    }

    /**
     * Send email verification
     */
    async sendEmailVerification(email: string, username: string, token: string): Promise<void> {
        try {
            const verificationUrl = this.buildVerificationUrl(token);
            const template = await this.loadTemplate('email-verification');

            const html = template({
                username,
                verificationUrl,
                appName: this.configService.get<string>('APP_NAME', 'APISportsGame'),
                supportEmail: this.configService.get<string>('SUPPORT_EMAIL', '<EMAIL>'),
            });

            await this.sendEmail({
                to: email,
                subject: 'Verify Your Email Address',
                html,
            });

            this.logger.log(`Email verification sent to: ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send email verification to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send password reset email
     */
    async sendPasswordReset(email: string, username: string, token: string): Promise<void> {
        try {
            const resetUrl = this.buildPasswordResetUrl(token);
            const template = await this.loadTemplate('password-reset');

            const html = template({
                username,
                resetUrl,
                appName: this.configService.get<string>('APP_NAME', 'APISportsGame'),
                supportEmail: this.configService.get<string>('SUPPORT_EMAIL', '<EMAIL>'),
            });

            await this.sendEmail({
                to: email,
                subject: 'Reset Your Password',
                html,
            });

            this.logger.log(`Password reset email sent to: ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send password reset email to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send welcome email
     */
    async sendWelcomeEmail(email: string, username: string): Promise<void> {
        try {
            const template = await this.loadTemplate('welcome');

            const html = template({
                username,
                appName: this.configService.get<string>('APP_NAME', 'APISportsGame'),
                dashboardUrl: this.configService.get<string>('FRONTEND_URL', 'http://localhost:3001'),
                supportEmail: this.configService.get<string>('SUPPORT_EMAIL', '<EMAIL>'),
            });

            await this.sendEmail({
                to: email,
                subject: 'Welcome to APISportsGame!',
                html,
            });

            this.logger.log(`Welcome email sent to: ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send welcome email to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send tier upgrade notification
     */
    async sendTierUpgradeNotification(email: string, username: string, newTier: string): Promise<void> {
        try {
            const template = await this.loadTemplate('tier-upgrade');

            const html = template({
                username,
                newTier: newTier.charAt(0).toUpperCase() + newTier.slice(1),
                appName: this.configService.get<string>('APP_NAME', 'APISportsGame'),
                dashboardUrl: this.configService.get<string>('FRONTEND_URL', 'http://localhost:3001'),
                supportEmail: this.configService.get<string>('SUPPORT_EMAIL', '<EMAIL>'),
            });

            await this.sendEmail({
                to: email,
                subject: `Tier Upgraded to ${newTier.charAt(0).toUpperCase() + newTier.slice(1)}!`,
                html,
            });

            this.logger.log(`Tier upgrade notification sent to: ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send tier upgrade notification to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Send API limit warning
     */
    async sendApiLimitWarning(email: string, username: string, usagePercentage: number): Promise<void> {
        try {
            const template = await this.loadTemplate('api-limit-warning');

            const html = template({
                username,
                usagePercentage,
                appName: this.configService.get<string>('APP_NAME', 'APISportsGame'),
                upgradeUrl: this.configService.get<string>('FRONTEND_URL', 'http://localhost:3001') + '/upgrade',
                supportEmail: this.configService.get<string>('SUPPORT_EMAIL', '<EMAIL>'),
            });

            await this.sendEmail({
                to: email,
                subject: 'API Usage Warning - Approaching Limit',
                html,
            });

            this.logger.log(`API limit warning sent to: ${email}`);
        } catch (error) {
            this.logger.error(`Failed to send API limit warning to ${email}:`, error);
            throw error;
        }
    }

    /**
     * Generic send email method
     */
    private async sendEmail(options: {
        to: string;
        subject: string;
        html: string;
        from?: string;
    }): Promise<void> {
        const mailOptions = {
            from: options.from || this.configService.get<string>('SMTP_FROM', '<EMAIL>'),
            to: options.to,
            subject: options.subject,
            html: options.html,
        };

        await this.transporter.sendMail(mailOptions);
    }

    /**
     * Load and compile email template
     */
    private async loadTemplate(templateName: string): Promise<handlebars.TemplateDelegate> {
        try {
            const templatePath = path.join(process.cwd(), 'src', 'auth', 'templates', `${templateName}.hbs`);
            const templateSource = fs.readFileSync(templatePath, 'utf8');
            return handlebars.compile(templateSource);
        } catch (error) {
            this.logger.error(`Failed to load email template: ${templateName}`, error);
            // Fallback to simple template
            return handlebars.compile(this.getDefaultTemplate(templateName));
        }
    }

    /**
     * Build email verification URL
     */
    private buildVerificationUrl(token: string): string {
        const baseUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3001');
        return `${baseUrl}/verify-email?token=${token}`;
    }

    /**
     * Build password reset URL
     */
    private buildPasswordResetUrl(token: string): string {
        const baseUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3001');
        return `${baseUrl}/reset-password?token=${token}`;
    }

    /**
     * Get default template fallback
     */
    private getDefaultTemplate(templateName: string): string {
        const templates: Record<string, string> = {
            'email-verification': `
                <h2>Verify Your Email Address</h2>
                <p>Hello {{username}},</p>
                <p>Please click the link below to verify your email address:</p>
                <a href="{{verificationUrl}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a>
                <p>If you didn't create an account, please ignore this email.</p>
                <p>Best regards,<br>{{appName}} Team</p>
            `,
            'password-reset': `
                <h2>Reset Your Password</h2>
                <p>Hello {{username}},</p>
                <p>Click the link below to reset your password:</p>
                <a href="{{resetUrl}}" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
                <p>This link will expire in 1 hour.</p>
                <p>If you didn't request this, please ignore this email.</p>
                <p>Best regards,<br>{{appName}} Team</p>
            `,
            'welcome': `
                <h2>Welcome to {{appName}}!</h2>
                <p>Hello {{username}},</p>
                <p>Welcome to {{appName}}! Your account has been successfully created.</p>
                <p>You can now access our sports data API and start building amazing applications.</p>
                <a href="{{dashboardUrl}}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Dashboard</a>
                <p>Best regards,<br>{{appName}} Team</p>
            `,
            'tier-upgrade': `
                <h2>Tier Upgraded!</h2>
                <p>Hello {{username}},</p>
                <p>Congratulations! Your account has been upgraded to {{newTier}} tier.</p>
                <p>You now have access to enhanced features and higher API limits.</p>
                <a href="{{dashboardUrl}}" style="background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Dashboard</a>
                <p>Best regards,<br>{{appName}} Team</p>
            `,
            'api-limit-warning': `
                <h2>API Usage Warning</h2>
                <p>Hello {{username}},</p>
                <p>You have used {{usagePercentage}}% of your monthly API limit.</p>
                <p>Consider upgrading your plan to avoid service interruption.</p>
                <a href="{{upgradeUrl}}" style="background: #fd7e14; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Upgrade Plan</a>
                <p>Best regards,<br>{{appName}} Team</p>
            `,
        };

        return templates[templateName] || '<p>Email template not found</p>';
    }
}
