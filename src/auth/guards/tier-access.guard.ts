import { Injectable, CanActivate, ExecutionContext, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RegisteredUserTier, UserType } from '../types/auth.types';
import { RegisteredUser } from '../entities/registered-user.entity';
import { SystemUser } from '../entities/system-user.entity';

/**
 * Tier Access Guard
 * Controls access based on registered user tiers
 */
@Injectable()
export class TierAccessGuard implements CanActivate {
    constructor(private reflector: Reflector) {}

    canActivate(context: ExecutionContext): boolean {
        // Get required tiers from decorator
        const requiredTiers = this.reflector.getAllAndOverride<RegisteredUserTier[]>('tiers', [
            context.getHandler(),
            context.getClass(),
        ]);

        // If no tiers specified, allow access
        if (!requiredTiers || requiredTiers.length === 0) {
            return true;
        }

        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            throw new UnauthorizedException('Authentication required');
        }

        // System users have full access
        if (user instanceof SystemUser) {
            return true;
        }

        // Check if user is RegisteredUser
        if (!(user instanceof RegisteredUser)) {
            throw new ForbiddenException('Invalid user type for tier-based access');
        }

        const registeredUser = user as RegisteredUser;

        // Check if user is active
        if (!registeredUser.isActive) {
            throw new ForbiddenException('Account is inactive');
        }

        // Check email verification if required
        const requireEmailVerification = this.reflector.getAllAndOverride<boolean>('requireEmailVerification', [
            context.getHandler(),
            context.getClass(),
        ]);

        if (requireEmailVerification && !registeredUser.isEmailVerified) {
            throw new ForbiddenException('Email verification required');
        }

        // Check if user has required tier
        if (!requiredTiers.includes(registeredUser.tier as RegisteredUserTier)) {
            throw new ForbiddenException(`Access denied. Required tier: ${requiredTiers.join(' or ')}, current tier: ${registeredUser.tier}`);
        }

        // Check if subscription is active (for premium/enterprise)
        if ((registeredUser.isPremium() || registeredUser.isEnterprise()) && !registeredUser.hasActiveSubscription()) {
            throw new ForbiddenException('Active subscription required');
        }

        // Check API limits
        if (registeredUser.hasExceededApiLimit()) {
            throw new ForbiddenException('API call limit exceeded for current tier');
        }

        return true;
    }
}

/**
 * API Usage Guard
 * Tracks and enforces API usage limits
 */
@Injectable()
export class ApiUsageGuard implements CanActivate {
    constructor() {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        // Skip for system users
        if (!user || user instanceof SystemUser) {
            return true;
        }

        // Check if user is RegisteredUser
        if (!(user instanceof RegisteredUser)) {
            return true; // Let other guards handle this
        }

        const registeredUser = user as RegisteredUser;

        // Check API limits
        if (registeredUser.hasExceededApiLimit()) {
            throw new ForbiddenException(`API call limit exceeded. Used: ${registeredUser.apiCallsUsed}/${registeredUser.apiCallsLimit || 'unlimited'}`);
        }

        // Increment API usage (this will be saved by the interceptor)
        registeredUser.incrementApiUsage();

        return true;
    }
}

/**
 * Premium Feature Guard
 * Restricts access to premium features
 */
@Injectable()
export class PremiumFeatureGuard implements CanActivate {
    constructor(private reflector: Reflector) {}

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            throw new UnauthorizedException('Authentication required');
        }

        // System users have full access
        if (user instanceof SystemUser) {
            return true;
        }

        // Check if user is RegisteredUser
        if (!(user instanceof RegisteredUser)) {
            throw new ForbiddenException('Invalid user type');
        }

        const registeredUser = user as RegisteredUser;

        // Check if user can access premium features
        if (!registeredUser.canAccessPremiumFeatures()) {
            throw new ForbiddenException('Premium subscription required');
        }

        return true;
    }
}

/**
 * Enterprise Feature Guard
 * Restricts access to enterprise features
 */
@Injectable()
export class EnterpriseFeatureGuard implements CanActivate {
    constructor(private reflector: Reflector) {}

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            throw new UnauthorizedException('Authentication required');
        }

        // System users have full access
        if (user instanceof SystemUser) {
            return true;
        }

        // Check if user is RegisteredUser
        if (!(user instanceof RegisteredUser)) {
            throw new ForbiddenException('Invalid user type');
        }

        const registeredUser = user as RegisteredUser;

        // Check if user can access enterprise features
        if (!registeredUser.canAccessEnterpriseFeatures()) {
            throw new ForbiddenException('Enterprise subscription required');
        }

        return true;
    }
}

/**
 * Email Verification Guard
 * Ensures user has verified their email
 */
@Injectable()
export class EmailVerificationGuard implements CanActivate {
    constructor() {}

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            throw new UnauthorizedException('Authentication required');
        }

        // System users don't need email verification
        if (user instanceof SystemUser) {
            return true;
        }

        // Check if user is RegisteredUser
        if (!(user instanceof RegisteredUser)) {
            return true; // Let other guards handle this
        }

        const registeredUser = user as RegisteredUser;

        // Check email verification
        if (!registeredUser.isEmailVerified) {
            throw new ForbiddenException('Email verification required');
        }

        return true;
    }
}
