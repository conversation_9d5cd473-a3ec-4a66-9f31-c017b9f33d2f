import {
    Injectable,
    NestInterceptor,
    Execution<PERSON>ontext,
    <PERSON><PERSON><PERSON><PERSON>,
    Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegisteredUser } from '../entities/registered-user.entity';
import { SystemUser } from '../entities/system-user.entity';

/**
 * API Usage Interceptor
 * Tracks API usage for registered users
 */
@Injectable()
export class ApiUsageInterceptor implements NestInterceptor {
    private readonly logger = new Logger(ApiUsageInterceptor.name);

    constructor(
        @InjectRepository(RegisteredUser)
        private readonly registeredUserRepository: Repository<RegisteredUser>,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        // Only track for registered users
        if (!user || user instanceof SystemUser) {
            return next.handle();
        }

        if (!(user instanceof RegisteredUser)) {
            return next.handle();
        }

        const registeredUser = user as RegisteredUser;

        // Track API usage after successful response
        return next.handle().pipe(
            tap({
                next: async () => {
                    try {
                        // Save updated API usage
                        await this.registeredUserRepository.save(registeredUser);
                        
                        this.logger.debug(
                            `API usage tracked for user ${registeredUser.username}: ${registeredUser.apiCallsUsed}/${registeredUser.apiCallsLimit || 'unlimited'}`
                        );
                    } catch (error) {
                        this.logger.error(`Failed to track API usage for user ${registeredUser.username}: ${error.message}`);
                    }
                },
                error: (error) => {
                    // Don't track usage on error responses
                    this.logger.debug(`API call failed for user ${registeredUser.username}, not tracking usage`);
                }
            })
        );
    }
}

/**
 * User Activity Interceptor
 * Updates last activity timestamp for users
 */
@Injectable()
export class UserActivityInterceptor implements NestInterceptor {
    private readonly logger = new Logger(UserActivityInterceptor.name);

    constructor(
        @InjectRepository(RegisteredUser)
        private readonly registeredUserRepository: Repository<RegisteredUser>,
        @InjectRepository(SystemUser)
        private readonly systemUserRepository: Repository<SystemUser>,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            return next.handle();
        }

        // Update last activity after successful response
        return next.handle().pipe(
            tap({
                next: async () => {
                    try {
                        const now = new Date();

                        if (user instanceof SystemUser) {
                            user.lastLoginAt = now;
                            await this.systemUserRepository.save(user);
                        } else if (user instanceof RegisteredUser) {
                            user.lastLoginAt = now;
                            await this.registeredUserRepository.save(user);
                        }

                        this.logger.debug(`Activity updated for user: ${user.username || user.email}`);
                    } catch (error) {
                        this.logger.error(`Failed to update user activity: ${error.message}`);
                    }
                }
            })
        );
    }
}

/**
 * Request Logging Interceptor
 * Logs API requests with user context
 */
@Injectable()
export class RequestLoggingInterceptor implements NestInterceptor {
    private readonly logger = new Logger(RequestLoggingInterceptor.name);

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const user = request.user;
        
        const method = request.method;
        const url = request.url;
        const userAgent = request.headers['user-agent'];
        const ip = request.ip || request.connection.remoteAddress;
        
        const startTime = Date.now();

        // Log request
        const userInfo = user 
            ? `${user.username || user.email} (${user instanceof SystemUser ? 'system' : 'registered'})`
            : 'anonymous';

        this.logger.log(`${method} ${url} - User: ${userInfo} - IP: ${ip}`);

        return next.handle().pipe(
            tap({
                next: () => {
                    const duration = Date.now() - startTime;
                    this.logger.log(
                        `${method} ${url} - ${response.statusCode} - ${duration}ms - User: ${userInfo}`
                    );
                },
                error: (error) => {
                    const duration = Date.now() - startTime;
                    this.logger.error(
                        `${method} ${url} - ERROR - ${duration}ms - User: ${userInfo} - Error: ${error.message}`
                    );
                }
            })
        );
    }
}

/**
 * Rate Limit Info Interceptor
 * Adds rate limit information to response headers
 */
@Injectable()
export class RateLimitInfoInterceptor implements NestInterceptor {
    private readonly logger = new Logger(RateLimitInfoInterceptor.name);

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const user = request.user;

        return next.handle().pipe(
            tap(() => {
                // Add API usage info to headers for registered users
                if (user && user instanceof RegisteredUser) {
                    const registeredUser = user as RegisteredUser;
                    
                    response.setHeader('X-API-Calls-Used', registeredUser.apiCallsUsed);
                    response.setHeader('X-API-Calls-Limit', registeredUser.apiCallsLimit || 'unlimited');
                    
                    const remaining = registeredUser.getApiCallsRemaining();
                    response.setHeader('X-API-Calls-Remaining', remaining !== null ? remaining : 'unlimited');
                    
                    response.setHeader('X-User-Tier', registeredUser.tier);
                    
                    // Calculate reset time (first day of next month)
                    const now = new Date();
                    const resetTime = new Date(now.getFullYear(), now.getMonth() + 1, 1);
                    response.setHeader('X-API-Reset-Time', resetTime.toISOString());
                }
            })
        );
    }
}
