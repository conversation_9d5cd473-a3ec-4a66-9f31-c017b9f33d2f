import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemUser } from '../entities/system-user.entity';
import { SystemUserJwtPayload, UserType } from '../../shared/types/auth.types';

/**
 * System JWT Strategy
 * Validates JWT tokens for system users and injects user context
 */
@Injectable()
export class SystemJwtStrategy extends PassportStrategy(Strategy, 'system-jwt') {
    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        private configService: ConfigService,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get<string>('JWT_SECRET', 'default-secret'),
        });
    }

    async validate(payload: SystemUserJwtPayload): Promise<SystemUser> {
        // Ensure this is a system user token
        if (payload.userType !== UserType.SYSTEM) {
            throw new UnauthorizedException('Invalid token type for system authentication');
        }

        // Find user in database
        const user = await this.systemUserRepository.findOne({
            where: { id: payload.sub }
        });

        if (!user) {
            throw new UnauthorizedException('System user not found');
        }

        if (!user.isActive) {
            throw new UnauthorizedException('System user account is inactive');
        }

        // Update last login timestamp
        user.lastLoginAt = new Date();
        await this.systemUserRepository.save(user);

        // Add userType to user object for guards
        (user as any).userType = 'system';

        return user;
    }
}
