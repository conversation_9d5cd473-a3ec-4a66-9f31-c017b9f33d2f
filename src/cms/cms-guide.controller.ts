import { <PERSON>, <PERSON>, Header, <PERSON><PERSON> } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { Public } from '../auth/decorators/auth.decorators';

/**
 * CMS Guide Controller
 * Serves CMS_DEVELOPMENT_GUIDE.md for AI agents
 */
@ApiTags('CMS Development')
@Controller('cms-guide')
export class CmsGuideController {
    private readonly guidePath = path.join(process.cwd(), 'CMS_DEVELOPMENT_GUIDE.md');

    /**
     * Get CMS Development Guide content
     * GET /cms-guide
     */
    @ApiOperation({
        summary: 'Get CMS Development Guide',
        description: 'Returns the latest CMS_DEVELOPMENT_GUIDE.md content for AI agents'
    })
    @ApiResponse({
        status: 200,
        description: 'CMS Development Guide content',
        schema: {
            type: 'string',
            example: '# APISportsGame CMS Development Guide\n\n...'
        }
    })
    @Public()
    @Get()
    @Header('Content-Type', 'text/plain; charset=utf-8')
    async getCmsGuide(@Res() res: Response): Promise<void> {
        try {
            const content = fs.readFileSync(this.guidePath, 'utf8');
            const stats = fs.statSync(this.guidePath);
            
            // Add metadata headers
            res.setHeader('Last-Modified', stats.mtime.toUTCString());
            res.setHeader('X-File-Size', stats.size.toString());
            res.setHeader('X-Last-Updated', stats.mtime.toISOString());
            res.setHeader('X-Version', this.extractVersion(content));
            
            res.send(content);
        } catch (error) {
            res.status(500).send('Error reading CMS Development Guide');
        }
    }

    /**
     * Get CMS Guide metadata
     * GET /cms-guide/metadata
     */
    @ApiOperation({
        summary: 'Get CMS Guide Metadata',
        description: 'Returns metadata about the CMS Development Guide file'
    })
    @ApiResponse({
        status: 200,
        description: 'File metadata',
        schema: {
            type: 'object',
            properties: {
                lastModified: { type: 'string', format: 'date-time' },
                size: { type: 'number' },
                version: { type: 'string' },
                status: { type: 'string' }
            }
        }
    })
    @Public()
    @Get('metadata')
    async getCmsGuideMetadata(): Promise<{
        lastModified: string;
        size: number;
        version: string;
        status: string;
    }> {
        try {
            const stats = fs.statSync(this.guidePath);
            const content = fs.readFileSync(this.guidePath, 'utf8');
            
            return {
                lastModified: stats.mtime.toISOString(),
                size: stats.size,
                version: this.extractVersion(content),
                status: 'available'
            };
        } catch (error) {
            return {
                lastModified: '',
                size: 0,
                version: 'unknown',
                status: 'error'
            };
        }
    }

    /**
     * Check if guide file exists and is readable
     * GET /cms-guide/health
     */
    @ApiOperation({
        summary: 'Health Check for CMS Guide',
        description: 'Checks if the CMS Development Guide file is accessible'
    })
    @Public()
    @Get('health')
    async healthCheck(): Promise<{
        status: string;
        fileExists: boolean;
        lastChecked: string;
    }> {
        const fileExists = fs.existsSync(this.guidePath);
        
        return {
            status: fileExists ? 'healthy' : 'error',
            fileExists,
            lastChecked: new Date().toISOString()
        };
    }

    /**
     * Extract version from guide content
     */
    private extractVersion(content: string): string {
        const versionMatch = content.match(/\*\*Version:\*\* (.+)/);
        return versionMatch ? versionMatch[1] : '1.0.0';
    }
}
