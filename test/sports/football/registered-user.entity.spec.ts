import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { DataSource } from 'typeorm';
import configuration from '../../../src/core/config/configuration';
import { RegisteredUser } from '../../../src/auth/entities/registered-user.entity';
import { DatabaseService } from '../../../src/core/database/database.service';

describe('RegisteredUser Entity', () => {
  let dataSource: DataSource;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({ load: [configuration] }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          useClass: DatabaseService,
        }),
        TypeOrmModule.forFeature([RegisteredUser]),
      ],
    }).compile();

    dataSource = module.get(DataSource);
    await dataSource.synchronize(true); // Reset DB
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  it('should save and retrieve registered user', async () => {
    const userData = {
      username: 'user1',
      email: '<EMAIL>',
      passwordHash: '$2b$10$examplehash',
      fullName: 'Test User',
      isActive: true,
      lastLoginAt: new Date('2025-05-14T10:00:00Z'),
    };

    const repository = dataSource.getRepository(RegisteredUser);
    const savedUser = await repository.save(userData);
    const retrievedUser = await repository.findOneBy({ username: 'user1' });

    expect(retrievedUser).toBeDefined();
    expect(retrievedUser?.email).toBe('<EMAIL>');
    expect(retrievedUser?.lastLoginAt?.toISOString()).toBe('2025-05-14T10:00:00.000Z'); // UTC
    expect(retrievedUser?.createdAt.toISOString()).toMatch(/Z$/); // UTC
  });
});
