# API Service Environment Variables

# Server Configuration
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=sports_game

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# External API Configuration
API_FOOTBALL_URL=https://v3.football.api-sports.io
API_FOOTBALL_KEY=your_api_key_here

# Email Configuration
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=<EMAIL>

# Application Configuration
APP_NAME=APISportsGame
FRONTEND_URL=http://localhost:3001
SUPPORT_EMAIL=<EMAIL>

# Security Configuration
BCRYPT_SALT_ROUNDS=12

# Environment
NODE_ENV=development
